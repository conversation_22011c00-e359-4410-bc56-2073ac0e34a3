local Rayfield = loadstring(game:HttpGet('https://sirius.menu/rayfield'))()

-- Add required services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")

local LocalPlayer = Players.LocalPlayer
local PlayerGui = LocalPlayer.PlayerGui
local GameEvents = ReplicatedStorage.GameEvents

-- Cache frequently accessed objects
local workspace = workspace
local camera = workspace.CurrentCamera

-- Seed stock variables
local SeedStock = {}
local SelectedSeedStock = { Selected = {} }
local lastStockCheck = {}
local autoBuyEnabled = false
local stockCheckConnection = nil

-- Gear stock variables
local GearStock = {}
local SelectedGearStock = { Selected = {} }
local lastGearStockCheck = {}
local gearAutoBuyEnabled = false
local gearStockCheckConnection = nil

-- Egg stock variables
local EggStock = {}
local SelectedEggStock = { Selected = {} }
local lastEggStockCheck = {}
local eggAutoBuyEnabled = false
local eggStockCheckConnection = nil

-- Performance optimization: Cache shop references
local cachedSeedShop = nil
local cachedGearShop = nil
local cachedEggShop = nil

-- Ultra-performance optimization: Pre-processed selection caches
local processedSeedSelection = {}
local processedGearSelection = {}
local processedEggSelection = {}
local selectionCacheValid = {seeds = false, gears = false, eggs = false}

-- Batch buying optimization
local buyQueue = {seeds = {}, gears = {}, eggs = {}}
local lastBuyTime = {seeds = 0, gears = 0, eggs = 0}
local buyDelay = 0.1 -- Minimum delay between batch buys (100ms)

-- Stock checking optimization
local stockCheckInterval = 0.5 -- Check stock every 500ms instead of every frame
local lastStockCheckTime = {seeds = 0, gears = 0, eggs = 0}

-- Function to get seed stock (optimized)
local function GetSeedStock(IgnoreNoStock)
	-- Use cached shop reference if available
	if not cachedSeedShop or not cachedSeedShop.Parent then
		cachedSeedShop = PlayerGui:FindFirstChild("Seed_Shop")
		if not cachedSeedShop then return IgnoreNoStock and {} or SeedStock end
	end

	local Items = cachedSeedShop:FindFirstChild("Blueberry", true)
	if not Items then return IgnoreNoStock and {} or SeedStock end
	Items = Items.Parent

	local NewList = {}
	local children = Items:GetChildren()

	for i = 1, #children do
		local Item = children[i]
		local MainFrame = Item:FindFirstChild("Main_Frame")
		if MainFrame then
			local StockText = MainFrame:FindFirstChild("Stock_Text")
			if StockText then
				local StockCount = tonumber(StockText.Text:match("%d+")) or 0

				if IgnoreNoStock then
					if StockCount > 0 then
						NewList[Item.Name] = StockCount
					end
				else
					SeedStock[Item.Name] = StockCount
				end
			end
		end
	end

	return IgnoreNoStock and NewList or SeedStock
end

-- Function to buy seed
local function BuySeed(Seed)
	local buyEvent = GameEvents:FindFirstChild("BuySeedStock")
	if buyEvent then
		buyEvent:FireServer(Seed)
	end
end

-- Function to get gear stock (optimized)
local function GetGearStock(IgnoreNoStock)
	-- Use cached shop reference if available
	if not cachedGearShop or not cachedGearShop.Parent then
		cachedGearShop = PlayerGui:FindFirstChild("Gear_Shop")
		if not cachedGearShop then return IgnoreNoStock and {} or GearStock end
	end

	-- Try to find gear items - assuming similar structure to seed shop
	local Items = cachedGearShop:FindFirstChild("WateringCan")
	if not Items then
		-- Try to find any frame that contains gear items
		local descendants = cachedGearShop:GetDescendants()
		for i = 1, #descendants do
			local child = descendants[i]
			if child:IsA("Frame") and child:FindFirstChild("Main_Frame") then
				Items = child.Parent
				break
			end
		end
	end

	if not Items then
		return IgnoreNoStock and {} or GearStock
	end

	local NewList = {}
	local children = Items:GetChildren()

	for i = 1, #children do
		local Item = children[i]
		local MainFrame = Item:FindFirstChild("Main_Frame")
		if MainFrame then
			local StockText = MainFrame:FindFirstChild("Stock_Text")
			if StockText then
				local StockCount = tonumber(StockText.Text:match("%d+")) or 0

				if IgnoreNoStock then
					if StockCount > 0 then
						NewList[Item.Name] = StockCount
					end
				else
					GearStock[Item.Name] = StockCount
				end
			end
		end
	end

	return IgnoreNoStock and NewList or GearStock
end

-- Function to buy gear
local function BuyGear(Gear)
	local buyEvent = GameEvents:FindFirstChild("BuyGearStock")
	if buyEvent then
		buyEvent:FireServer(Gear)
	end
end

-- Function to get egg stock (optimized)
local function GetEggStock(IgnoreNoStock)
	-- Use cached shop reference if available
	if not cachedEggShop or not cachedEggShop.Parent then
		cachedEggShop = PlayerGui:FindFirstChild("PetShop_UI")
			or PlayerGui:FindFirstChild("Pet_Egg_Shop")
			or PlayerGui:FindFirstChild("Egg_Shop")
			or PlayerGui:FindFirstChild("PetEggShop")
			or PlayerGui:FindFirstChild("EggShop")

		if not cachedEggShop then
			return IgnoreNoStock and {} or EggStock
		end
	end

	-- Try to find egg items container - look for common patterns
	local Items = nil
	local descendants = cachedEggShop:GetDescendants()

	-- Method 1: Look for frames with Main_Frame children
	for i = 1, #descendants do
		local child = descendants[i]
		if child:IsA("Frame") and child:FindFirstChild("Main_Frame") then
			Items = child.Parent
			break
		end
	end

	-- Method 2: Look for ScrollingFrame or Frame that contains multiple items
	if not Items then
		for i = 1, #descendants do
			local child = descendants[i]
			if child:IsA("ScrollingFrame") or child:IsA("Frame") then
				local childCount = 0
				local children = child:GetChildren()
				for j = 1, #children do
					if children[j]:IsA("Frame") then
						childCount = childCount + 1
					end
				end
				if childCount > 1 then
					Items = child
					break
				end
			end
		end
	end

	if not Items then
		return IgnoreNoStock and {} or EggStock
	end

	local NewList = {}
	local children = Items:GetChildren()

	for i = 1, #children do
		local Item = children[i]
		if Item:IsA("Frame") then
			local MainFrame = Item:FindFirstChild("Main_Frame")
			if MainFrame then
				local StockText = MainFrame:FindFirstChild("Stock_Text")
				if StockText then
					local StockCount = tonumber(StockText.Text:match("%d+")) or 0

					if IgnoreNoStock then
						if StockCount > 0 then
							NewList[Item.Name] = StockCount
						end
					else
						EggStock[Item.Name] = StockCount
					end
				end
			end
		end
	end

	return IgnoreNoStock and NewList or EggStock
end

-- Function to buy egg (optimized)
local cachedEggBuyEvent = nil
local function BuyEgg(Egg)
	-- Cache the buy event for better performance
	if not cachedEggBuyEvent or not cachedEggBuyEvent.Parent then
		local possibleNames = {
			"BuyEggStock",
			"BuyEgg",
			"PurchaseEgg",
			"BuyPetEgg",
			"PetEggPurchase",
			"EggPurchase",
			"BuyPet",
			"PurchasePet"
		}

		for i = 1, #possibleNames do
			local event = GameEvents:FindFirstChild(possibleNames[i])
			if event and event:IsA("RemoteEvent") then
				cachedEggBuyEvent = event
				break
			end
		end
	end

	if cachedEggBuyEvent then
		cachedEggBuyEvent:FireServer(Egg)
	end
end

-- Ultra-optimized function to process selected items (shared logic)
local function processSelectedItems(selectedTable, cacheTable, cacheKey)
    if not selectionCacheValid[cacheKey] then
        -- Clear and rebuild cache
        for i = #cacheTable, 1, -1 do
            cacheTable[i] = nil
        end

        if type(selectedTable) == "table" then
            for k, v in pairs(selectedTable) do
                if v == true or (type(v) == "string" and v ~= "") then
                    cacheTable[#cacheTable + 1] = type(k) == "string" and k or v
                elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                    cacheTable[#cacheTable + 1] = v
                end
            end
        end
        selectionCacheValid[cacheKey] = true
    end
    return cacheTable
end

-- Ultra-optimized batch buying function
local function executeBatchBuy(itemType, buyFunction, stockTable, queueTable)
    local currentTime = tick()
    local lastTime = lastBuyTime[itemType]

    if currentTime - lastTime < buyDelay then return end
    lastBuyTime[itemType] = currentTime

    -- Process queue in batches
    for i = 1, math.min(#queueTable, 10) do -- Limit to 10 items per batch
        local itemName = queueTable[i]
        local stock = stockTable[itemName]

        if stock and stock > 0 then
            -- Buy all stock at once for better performance
            for j = 1, stock do
                buyFunction(itemName)
            end
        end
    end

    -- Clear processed items from queue
    for i = math.min(#queueTable, 10), 1, -1 do
        table.remove(queueTable, i)
    end
end

-- Function to buy all selected eggs (ultra-optimized)
local function BuyAllSelectedEggs()
    local eggsToProcess = processSelectedItems(SelectedEggStock.Selected, processedEggSelection, "eggs")
    if #eggsToProcess == 0 then return end

    -- Update stock before buying
    GetEggStock()

    -- Add to buy queue instead of immediate buying
    for i = 1, #eggsToProcess do
        buyQueue.eggs[#buyQueue.eggs + 1] = eggsToProcess[i]
    end

    -- Execute batch buy
    executeBatchBuy("eggs", BuyEgg, EggStock, buyQueue.eggs)
end

-- Function to buy all selected gears (ultra-optimized)
local function BuyAllSelectedGears()
    local gearsToProcess = processSelectedItems(SelectedGearStock.Selected, processedGearSelection, "gears")
    if #gearsToProcess == 0 then return end

    -- Update stock before buying
    GetGearStock()

    -- Add to buy queue instead of immediate buying
    for i = 1, #gearsToProcess do
        buyQueue.gears[#buyQueue.gears + 1] = gearsToProcess[i]
    end

    -- Execute batch buy
    executeBatchBuy("gears", BuyGear, GearStock, buyQueue.gears)
end

-- Function to buy all selected seeds (ultra-optimized)
local function BuyAllSelectedSeeds()
    local seedsToProcess = processSelectedItems(SelectedSeedStock.Selected, processedSeedSelection, "seeds")
    if #seedsToProcess == 0 then return end

    -- Update stock before buying
    GetSeedStock()

    -- Add to buy queue instead of immediate buying
    for i = 1, #seedsToProcess do
        buyQueue.seeds[#buyQueue.seeds + 1] = seedsToProcess[i]
    end

    -- Execute batch buy
    executeBatchBuy("seeds", BuySeed, SeedStock, buyQueue.seeds)
end

-- Ultra-optimized stock checking with interval-based updates
local function startStockChecking()
    stockCheckConnection = RunService.Heartbeat:Connect(function()
        if not autoBuyEnabled or not SelectedSeedStock.Selected then return end

        local currentTime = tick()
        if currentTime - lastStockCheckTime.seeds < stockCheckInterval then return end
        lastStockCheckTime.seeds = currentTime

        -- Use cached processed selection
        local seedsToCheck = processSelectedItems(SelectedSeedStock.Selected, processedSeedSelection, "seeds")
        if #seedsToCheck == 0 then return end

        local currentStock = GetSeedStock()
        local shouldBuy = false

        -- Optimized stock change detection
        for i = 1, #seedsToCheck do
            local seedName = seedsToCheck[i]
            local previousStock = lastStockCheck[seedName] or -1
            local newStock = currentStock[seedName] or 0

            if previousStock ~= newStock then
                lastStockCheck[seedName] = newStock
                if newStock > 0 then
                    shouldBuy = true
                    break -- Exit early if we found stock
                end
            end
        end

        if shouldBuy then
            BuyAllSelectedSeeds()
        end
    end)
end

-- Function to stop stock checking
local function stopStockChecking()
    if stockCheckConnection then
        stockCheckConnection:Disconnect()
        stockCheckConnection = nil
    end
end

-- Ultra-optimized gear stock checking
local function startGearStockChecking()
    gearStockCheckConnection = RunService.Heartbeat:Connect(function()
        if not gearAutoBuyEnabled or not SelectedGearStock.Selected then return end

        local currentTime = tick()
        if currentTime - lastStockCheckTime.gears < stockCheckInterval then return end
        lastStockCheckTime.gears = currentTime

        -- Use cached processed selection
        local gearsToCheck = processSelectedItems(SelectedGearStock.Selected, processedGearSelection, "gears")
        if #gearsToCheck == 0 then return end

        local currentStock = GetGearStock()
        local shouldBuy = false

        -- Optimized stock change detection
        for i = 1, #gearsToCheck do
            local gearName = gearsToCheck[i]
            local previousStock = lastGearStockCheck[gearName] or -1
            local newStock = currentStock[gearName] or 0

            if previousStock ~= newStock then
                lastGearStockCheck[gearName] = newStock
                if newStock > 0 then
                    shouldBuy = true
                    break -- Exit early if we found stock
                end
            end
        end

        if shouldBuy then
            BuyAllSelectedGears()
        end
    end)
end

-- Function to stop gear stock checking
local function stopGearStockChecking()
    if gearStockCheckConnection then
        gearStockCheckConnection:Disconnect()
        gearStockCheckConnection = nil
    end
end

-- Ultra-optimized egg stock checking
local function startEggStockChecking()
    eggStockCheckConnection = RunService.Heartbeat:Connect(function()
        if not eggAutoBuyEnabled or not SelectedEggStock.Selected then return end

        local currentTime = tick()
        if currentTime - lastStockCheckTime.eggs < stockCheckInterval then return end
        lastStockCheckTime.eggs = currentTime

        -- Use cached processed selection
        local eggsToCheck = processSelectedItems(SelectedEggStock.Selected, processedEggSelection, "eggs")
        if #eggsToCheck == 0 then return end

        local currentStock = GetEggStock()
        local shouldBuy = false

        -- Optimized stock change detection
        for i = 1, #eggsToCheck do
            local eggName = eggsToCheck[i]
            local previousStock = lastEggStockCheck[eggName] or -1
            local newStock = currentStock[eggName] or 0

            if previousStock ~= newStock then
                lastEggStockCheck[eggName] = newStock
                if newStock > 0 then
                    shouldBuy = true
                    break -- Exit early if we found stock
                end
            end
        end

        if shouldBuy then
            BuyAllSelectedEggs()
        end
    end)
end

-- Function to stop egg stock checking
local function stopEggStockChecking()
    if eggStockCheckConnection then
        eggStockCheckConnection:Disconnect()
        eggStockCheckConnection = nil
    end
end

local Window = Rayfield:CreateWindow({
    Name = "หมา " .. 1.0,
    LoadingTitle = "by XZery",
    LoadingSubtitle = "Loading...",
    ConfigurationSaving = {
        Enabled = true,
        FolderName = "RayfieldScriptHub",
        FileName = "grow-a-garden"
    },
    Discord = {
        Enabled = false,
        Invite = "noinvitelink",
        RememberJoins = true
    },
    KeySystem = false,
    KeySettings = {
        Title = "Untitled",
        Subtitle = "Key System",
        Note = "No method of obtaining the key is provided",
        FileName = "Key",
        SaveKey = true,
        GrabKeyFromSite = false,
        Key = {"Hello"}
    }
})

local Tabs = {
    Main = Window:CreateTab("Main", 4483362458),
    Seed = Window:CreateTab("Seed", 4483362458),
    Gear = Window:CreateTab("Gear", 4483362458),
    Egg = Window:CreateTab("Egg", 4483362458)
}

local speed = Tabs.Main:CreateSlider({
    Name = "Speed",
    Range = {16, 200},
    Increment = 1,
    Suffix = "",
    CurrentValue = 16,
    Flag = "SpeedSlider",
    Callback = function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        if char and char:FindFirstChild("Humanoid") then
            char.Humanoid.WalkSpeed = Value
        end
    end,
})

local jump = Tabs.Main:CreateSlider({
    Name = "Jump Power",
    Range = {50, 200},
    Increment = 1,
    Suffix = "",
    CurrentValue = 50,
    Flag = "JumpSlider",
    Callback = function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        if char and char:FindFirstChild("Humanoid") then
            char.Humanoid.JumpPower = Value
        end
    end,
})

    -- Fly toggle functionality with mobile support (optimized)
    local flyEnabled = false
    local bodyVelocity = nil
    local bodyAngularVelocity = nil
    local connection = nil
    local flySpeed = 50

    -- Mobile fly controls
    local flyUpButton = nil
    local flyDownButton = nil
    local mobileControlsFrame = nil
    local isTouchEnabled = UserInputService.TouchEnabled

    -- Cache color values
    local upButtonColor = Color3.fromRGB(0, 162, 255)
    local downButtonColor = Color3.fromRGB(255, 87, 87)
    local whiteColor = Color3.fromRGB(255, 255, 255)

    local function createMobileControls()
        if not isTouchEnabled then return end

        -- Create mobile fly controls
        local screenGui = Instance.new("ScreenGui")
        screenGui.Name = "FlyControls"
        screenGui.Parent = LocalPlayer.PlayerGui
        screenGui.ResetOnSpawn = false

        mobileControlsFrame = Instance.new("Frame")
        mobileControlsFrame.Name = "FlyControlsFrame"
        mobileControlsFrame.Size = UDim2.new(0, 120, 0, 200)
        mobileControlsFrame.Position = UDim2.new(1, -130, 0.5, -100)
        mobileControlsFrame.BackgroundTransparency = 1
        mobileControlsFrame.Parent = screenGui

        -- Fly Up Button
        flyUpButton = Instance.new("TextButton")
        flyUpButton.Name = "FlyUpButton"
        flyUpButton.Size = UDim2.new(0, 100, 0, 80)
        flyUpButton.Position = UDim2.new(0, 10, 0, 10)
        flyUpButton.BackgroundColor3 = upButtonColor
        flyUpButton.BorderSizePixel = 0
        flyUpButton.Text = "UP"
        flyUpButton.TextColor3 = whiteColor
        flyUpButton.TextScaled = true
        flyUpButton.Font = Enum.Font.GothamBold
        flyUpButton.Parent = mobileControlsFrame

        -- Add rounded corners to up button
        local upCorner = Instance.new("UICorner")
        upCorner.CornerRadius = UDim.new(0, 10)
        upCorner.Parent = flyUpButton

        -- Fly Down Button
        flyDownButton = Instance.new("TextButton")
        flyDownButton.Name = "FlyDownButton"
        flyDownButton.Size = UDim2.new(0, 100, 0, 80)
        flyDownButton.Position = UDim2.new(0, 10, 0, 110)
        flyDownButton.BackgroundColor3 = downButtonColor
        flyDownButton.BorderSizePixel = 0
        flyDownButton.Text = "DOWN"
        flyDownButton.TextColor3 = whiteColor
        flyDownButton.TextScaled = true
        flyDownButton.Font = Enum.Font.GothamBold
        flyDownButton.Parent = mobileControlsFrame

        -- Add rounded corners to down button
        local downCorner = Instance.new("UICorner")
        downCorner.CornerRadius = UDim.new(0, 10)
        downCorner.Parent = flyDownButton
    end

    local function removeMobileControls()
        if mobileControlsFrame and mobileControlsFrame.Parent then
            mobileControlsFrame.Parent:Destroy()
        end
        flyUpButton = nil
        flyDownButton = nil
        mobileControlsFrame = nil
    end

    local function enableFly()
        local player = game.Players.LocalPlayer
        local char = player.Character
        if not char or not char:FindFirstChild("HumanoidRootPart") then return end

        local humanoidRootPart = char.HumanoidRootPart

        -- Create BodyVelocity for movement
        bodyVelocity = Instance.new("BodyVelocity")
        bodyVelocity.MaxForce = Vector3.new(9e9, 9e9, 9e9)
        bodyVelocity.Velocity = Vector3.new(0, 0, 0)
        bodyVelocity.Parent = humanoidRootPart

        -- Create BodyAngularVelocity for rotation control
        bodyAngularVelocity = Instance.new("BodyAngularVelocity")
        bodyAngularVelocity.MaxTorque = Vector3.new(0, 9e9, 0)
        bodyAngularVelocity.AngularVelocity = Vector3.new(0, 0, 0)
        bodyAngularVelocity.Parent = humanoidRootPart

        -- Create mobile controls if on mobile
        createMobileControls()

        -- Movement control (optimized)
        connection = RunService.Heartbeat:Connect(function()
            local velocity = Vector3.new(0, 0, 0)

            -- Get camera directions
            local lookDirection = camera.CFrame.LookVector
            local rightDirection = camera.CFrame.RightVector
            local upDirection = Vector3.new(0, 1, 0)

            -- PC Controls (Keyboard)
            if not isTouchEnabled then
                if UserInputService:IsKeyDown(Enum.KeyCode.W) then
                    velocity = velocity + lookDirection * flySpeed
                end
                if UserInputService:IsKeyDown(Enum.KeyCode.S) then
                    velocity = velocity - lookDirection * flySpeed
                end
                if UserInputService:IsKeyDown(Enum.KeyCode.A) then
                    velocity = velocity - rightDirection * flySpeed
                end
                if UserInputService:IsKeyDown(Enum.KeyCode.D) then
                    velocity = velocity + rightDirection * flySpeed
                end
                if UserInputService:IsKeyDown(Enum.KeyCode.Space) then
                    velocity = velocity + upDirection * flySpeed
                end
                if UserInputService:IsKeyDown(Enum.KeyCode.LeftShift) then
                    velocity = velocity - upDirection * flySpeed
                end
            else
                -- Mobile Controls
                local moveVector = char.Humanoid.MoveDirection
                if moveVector.Magnitude > 0 then
                    -- Use the movement direction from mobile control
                    local cameraCFrame = camera.CFrame
                    local relativeDirection = cameraCFrame:VectorToWorldSpace(Vector3.new(moveVector.X, 0, -moveVector.Z))
                    velocity = velocity + relativeDirection * flySpeed
                end

                -- Handle up/down movement with buttons
                if flyUpButton and flyUpButton.Parent then
                    -- Connect touch events for up button
                    if not flyUpButton:GetAttribute("Connected") then
                        flyUpButton:SetAttribute("Connected", true)
                        flyUpButton.MouseButton1Down:Connect(function()
                            flyUpButton:SetAttribute("Pressed", true)
                        end)
                        flyUpButton.MouseButton1Up:Connect(function()
                            flyUpButton:SetAttribute("Pressed", false)
                        end)
                    end

                    -- Connect touch events for down button
                    if flyDownButton and not flyDownButton:GetAttribute("Connected") then
                        flyDownButton:SetAttribute("Connected", true)
                        flyDownButton.MouseButton1Down:Connect(function()
                            flyDownButton:SetAttribute("Pressed", true)
                        end)
                        flyDownButton.MouseButton1Up:Connect(function()
                            flyDownButton:SetAttribute("Pressed", false)
                        end)
                    end

                    -- Check button states
                    if flyUpButton:GetAttribute("Pressed") then
                        velocity = velocity + upDirection * flySpeed
                    end
                    if flyDownButton and flyDownButton:GetAttribute("Pressed") then
                        velocity = velocity - upDirection * flySpeed
                    end
                end
            end

            bodyVelocity.Velocity = velocity
        end)
    end

    local function disableFly()
        if bodyVelocity then
            bodyVelocity:Destroy()
            bodyVelocity = nil
        end
        if bodyAngularVelocity then
            bodyAngularVelocity:Destroy()
            bodyAngularVelocity = nil
        end
        if connection then
            connection:Disconnect()
            connection = nil
        end
        -- Remove mobile controls
        removeMobileControls()
    end

    local flyToggle = Tabs.Main:CreateToggle({
        Name = "Fly",
        CurrentValue = false,
        Flag = "FlyToggle",
        Callback = function(Value)
            flyEnabled = Value
            if flyEnabled then
                enableFly()
            else
                disableFly()
            end
        end,
    })

    -- Reset Character Button
    local resetButton = Tabs.Main:CreateButton({
        Name = "Reset Character",
        Callback = function()
            local player = game.Players.LocalPlayer
            if player.Character then
                player.Character:BreakJoints()
            end
        end,
    })

    -- Seed stock dropdown (multi-select) - moved to Seed tab (ultra-optimized)
    local seedDropdown = Tabs.Seed:CreateDropdown({
        Name = "Select Seeds",
        Options = {},
        CurrentOption = {},
        MultipleOptions = true,
        Flag = "SeedDropdown",
        Callback = function(Value)
            SelectedSeedStock.Selected = Value
            selectionCacheValid.seeds = false -- Invalidate cache
            GetSeedStock()
        end,
    })

    -- Function to update seed dropdown
    local function updateSeedDropdown()
        local allSeeds = GetSeedStock(false) -- Get all seeds regardless of stock
        local seedList = {}

        for seedName, _ in pairs(allSeeds) do
            table.insert(seedList, seedName)
        end

        seedDropdown:Refresh(seedList, true)
    end

    -- Auto-buy toggle - moved to Seed tab
    local autoBuyToggle = Tabs.Seed:CreateToggle({
        Name = "Auto-Buy Seeds",
        CurrentValue = false,
        Flag = "AutoBuyToggle",
        Callback = function(Value)
            autoBuyEnabled = Value
            if autoBuyEnabled then
                startStockChecking()
            else
                stopStockChecking()
            end
        end,
    })

    -- Note: Callback is now handled in the dropdown creation above



    -- Initial seed dropdown update
    updateSeedDropdown()

    -- Load saved seed selections (will be loaded after SaveManager is properly initialized)

    -- Gear stock dropdown (multi-select) (ultra-optimized)
    local gearDropdown = Tabs.Gear:CreateDropdown({
        Name = "Select Gears",
        Options = {},
        CurrentOption = {},
        MultipleOptions = true,
        Flag = "GearDropdown",
        Callback = function(Value)
            SelectedGearStock.Selected = Value
            selectionCacheValid.gears = false -- Invalidate cache
            GetGearStock()
        end,
    })

    -- Function to update gear dropdown
    local function updateGearDropdown()
        local allGears = GetGearStock(false) -- Get all gears regardless of stock
        local gearList = {}

        for gearName, _ in pairs(allGears) do
            table.insert(gearList, gearName)
        end

        gearDropdown:Refresh(gearList, true)
    end

    -- Gear auto-buy toggle
    local gearAutoBuyToggle = Tabs.Gear:CreateToggle({
        Name = "Auto-Buy Gears",
        CurrentValue = false,
        Flag = "GearAutoBuyToggle",
        Callback = function(Value)
            gearAutoBuyEnabled = Value
            if gearAutoBuyEnabled then
                startGearStockChecking()
            else
                stopGearStockChecking()
            end
        end,
    })

    -- Note: Callback is now handled in the dropdown creation above

    -- Initial gear dropdown update
    updateGearDropdown()

    -- Egg stock dropdown (multi-select) (optimized)
    local eggDropdown = Tabs.Egg:CreateDropdown({
        Name = "Select Eggs",
        Options = {},
        CurrentOption = {},
        MultipleOptions = true,
        Flag = "EggDropdown",
        Callback = function(Value)
            SelectedEggStock.Selected = Value
            -- Update stock data when selection changes
            GetEggStock()
        end,
    })

    -- Function to update egg dropdown
    local function updateEggDropdown()
        local allEggs = GetEggStock(false) -- Get all eggs regardless of stock
        local eggList = {}

        for eggName, _ in pairs(allEggs) do
            table.insert(eggList, eggName)
        end

        eggDropdown:Refresh(eggList, true)
    end

    -- Egg auto-buy toggle
    local eggAutoBuyToggle = Tabs.Egg:CreateToggle({
        Name = "Auto-Buy Eggs",
        CurrentValue = false,
        Flag = "EggAutoBuyToggle",
        Callback = function(Value)
            eggAutoBuyEnabled = Value
            if eggAutoBuyEnabled then
                startEggStockChecking()
            else
                stopEggStockChecking()
            end
        end,
    })

    -- Initial egg dropdown update
    updateEggDropdown()

-- Rayfield has built-in configuration saving, so we don't need separate SaveManager/InterfaceManager

Rayfield:Notify({
    Title = "Script Loaded",
    Content = "The script has been loaded successfully.",
    Duration = 6.5,
    Image = 4483362458,
    Actions = {
        Ignore = {
            Name = "Okay!",
            Callback = function()
                -- Notification acknowledged
            end
        },
    },
})