local Rayfield = loadstring(game:HttpGet('https://sirius.menu/rayfield'))()

-- Add required services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")

local LocalPlayer = Players.LocalPlayer
local PlayerGui = LocalPlayer.PlayerGui
local GameEvents = ReplicatedStorage.GameEvents

-- Cache frequently accessed objects
local workspace = workspace
local camera = workspace.CurrentCamera

-- Anti-AFK System
local VirtualInputManager = Instance.new("VirtualInputManager")
local Hooks = {}

local function TrueString(String)
    if type(String) ~= "string" then
        return false
    end

    return (string.split(String, "\0"))[1]
end

local function SortArguments(self, ...)
    return self, {...}
end

local function hookGetSerivce(...)
    local OldGetService; OldGetService = function(...)
        local self, Index = ...
        local Response = OldGetService(...)

        if type(Index) == "string" and TrueString(Index) == "VirtualInputManager" then
            error(("'%s' is not a valid Service name"):format(TrueString(Index)))
            return;
        end

        return Response
    end
end

local OldFindService = hookfunction(game.FindService, function(...)
    local self, Index = ...
    local Response = OldFindService(...)

    if type(Index) == "string" and TrueString(Index) == "VirtualInputManager" then
        return;
    end

    return Response
end)

hookGetSerivce(game.GetService)
hookGetSerivce(game.getService)
hookGetSerivce(game.service)

local OldNamecall; OldNamecall = hookmetamethod(game, "__namecall", function(...)
    local self, Arguments = SortArguments(...)
    local Method = getnamecallmethod()

    if typeof(self) == "Instance" and self == game and Method:lower():match("service") and TrueString(Arguments[1]) == "VirtualInputManager" then
        if Method == "FindService" then
            return;
        end

        local Success, Error = pcall(function()
            setnamecallmethod(Method)
            game[Method](game, "VirtualFuckOff")
        end)

        if not Error:match("is not a valid member") then
            error(Error:replace("VirtualFuckOff", "VirtualInputManager"))
            return;
        end
    end

    return OldNamecall(...)
end)

local OldWindow; OldWindow = hookmetamethod(UserInputService.WindowFocused, "__index", function(...)
    local self, Index = ...
    local Response = OldWindow(...)

    if type(Response) ~= "function" and (tostring(self):find("WindowFocused") or tostring(self):find("WindowFocusReleased")) and not table.find(Hooks, Response) then
        table.insert(Hooks, Response)

        if Index:lower() == "wait" then
            local Old2; Old2 = hookfunction(Response, function(...)
                local self1 = ...

                if self1 == self then
                    self1 = Instance.new("BindableEvent").Event
                end

                return Old2(self1)
            end)
        elseif Index:lower() == "connect" then
            local Old2; Old2 = hookfunction(Response, function(...)
                local self1, Function = ...

                if self1 == self then
                    Function = function() return; end
                end

                return Old2(self1, Function)
            end)
        end
    end

    return Response
end)

for i, v in next, getconnections(UserInputService.WindowFocusReleased) do
    v:Disable()
end

for i, v in next, getconnections(UserInputService.WindowFocused) do
    v:Disable()
end

if not iswindowactive() and not getgenv().WindowFocused then
    firesignal(UserInputService.WindowFocused)
    getgenv().WindowFocused = true
end

-- Anti-AFK variables
local antiAfkEnabled = false
local antiAfkConnection = nil

-- Anti-AFK function
local function startAntiAfk()
    if antiAfkConnection then return end

    antiAfkConnection = spawn(function()
        while antiAfkEnabled do
            VirtualInputManager:SendKeyEvent(true, Enum.KeyCode.Unknown, false, game)
            task.wait(Random.new():NextNumber(15, 120))
        end
    end)
end

local function stopAntiAfk()
    antiAfkEnabled = false
    if antiAfkConnection then
        antiAfkConnection = nil
    end
end

-- Seed stock variables
local SeedStock = {}
local SelectedSeedStock = { Selected = {} }
local lastStockCheck = {}
local autoBuyEnabled = false
local stockCheckConnection = nil

-- Gear stock variables
local GearStock = {}
local SelectedGearStock = { Selected = {} }
local lastGearStockCheck = {}
local gearAutoBuyEnabled = false
local gearStockCheckConnection = nil

-- Egg stock variables
local EggStock = {}
local SelectedEggStock = { Selected = {} }
local lastEggStockCheck = {}
local eggAutoBuyEnabled = false
local eggStockCheckConnection = nil

-- Remove caching as requested by user

-- Function to get seed stock (simplified)
local function GetSeedStock(IgnoreNoStock)
	local SeedShop = PlayerGui:FindFirstChild("Seed_Shop")
	if not SeedShop then return IgnoreNoStock and {} or SeedStock end

	local Items = SeedShop:FindFirstChild("Blueberry", true)
	if not Items then return IgnoreNoStock and {} or SeedStock end
	Items = Items.Parent

	local NewList = {}

	for _, Item in pairs(Items:GetChildren()) do
		local MainFrame = Item:FindFirstChild("Main_Frame")
		if MainFrame then
			local StockText = MainFrame:FindFirstChild("Stock_Text")
			if StockText then
				local StockCount = tonumber(StockText.Text:match("%d+")) or 0

				if IgnoreNoStock then
					if StockCount > 0 then
						NewList[Item.Name] = StockCount
					end
				else
					SeedStock[Item.Name] = StockCount
				end
			end
		end
	end

	return IgnoreNoStock and NewList or SeedStock
end

-- Function to buy seed
local function BuySeed(Seed)
	local buyEvent = GameEvents:FindFirstChild("BuySeedStock")
	if buyEvent then
		buyEvent:FireServer(Seed)
	end
end

-- Function to get gear stock (simplified)
local function GetGearStock(IgnoreNoStock)
	local GearShop = PlayerGui:FindFirstChild("Gear_Shop")
	if not GearShop then return IgnoreNoStock and {} or GearStock end

	-- Try to find gear items - assuming similar structure to seed shop
	local Items = GearShop:FindFirstChild("WateringCan")
	if not Items then
		-- Try to find any frame that contains gear items
		for _, child in pairs(GearShop:GetDescendants()) do
			if child:IsA("Frame") and child:FindFirstChild("Main_Frame") then
				Items = child.Parent
				break
			end
		end
	end

	if not Items then
		return IgnoreNoStock and {} or GearStock
	end

	local NewList = {}

	for _, Item in pairs(Items:GetChildren()) do
		local MainFrame = Item:FindFirstChild("Main_Frame")
		if MainFrame then
			local StockText = MainFrame:FindFirstChild("Stock_Text")
			if StockText then
				local StockCount = tonumber(StockText.Text:match("%d+")) or 0

				if IgnoreNoStock then
					if StockCount > 0 then
						NewList[Item.Name] = StockCount
					end
				else
					GearStock[Item.Name] = StockCount
				end
			end
		end
	end

	return IgnoreNoStock and NewList or GearStock
end

-- Function to buy gear
local function BuyGear(Gear)
	local buyEvent = GameEvents:FindFirstChild("BuyGearStock")
	if buyEvent then
		buyEvent:FireServer(Gear)
	end
end

-- Function to get egg stock (simplified)
local function GetEggStock(IgnoreNoStock)
	-- Try multiple possible names for egg shop
	local EggShop = PlayerGui:FindFirstChild("PetShop_UI")
		or PlayerGui:FindFirstChild("Pet_Egg_Shop")
		or PlayerGui:FindFirstChild("Egg_Shop")
		or PlayerGui:FindFirstChild("PetEggShop")
		or PlayerGui:FindFirstChild("EggShop")

	if not EggShop then
		return IgnoreNoStock and {} or EggStock
	end

	-- Try to find egg items container - look for common patterns
	local Items = nil

	-- Method 1: Look for frames with Main_Frame children
	for _, child in pairs(EggShop:GetDescendants()) do
		if child:IsA("Frame") and child:FindFirstChild("Main_Frame") then
			Items = child.Parent
			break
		end
	end

	-- Method 2: Look for ScrollingFrame or Frame that contains multiple items
	if not Items then
		for _, child in pairs(EggShop:GetDescendants()) do
			if child:IsA("ScrollingFrame") or child:IsA("Frame") then
				local childCount = 0
				for _, subchild in pairs(child:GetChildren()) do
					if subchild:IsA("Frame") then
						childCount = childCount + 1
					end
				end
				if childCount > 1 then
					Items = child
					break
				end
			end
		end
	end

	if not Items then
		return IgnoreNoStock and {} or EggStock
	end

	local NewList = {}

	for _, Item in pairs(Items:GetChildren()) do
		if Item:IsA("Frame") then
			local MainFrame = Item:FindFirstChild("Main_Frame")
			if MainFrame then
				local StockText = MainFrame:FindFirstChild("Stock_Text")
				if StockText then
					local StockCount = tonumber(StockText.Text:match("%d+")) or 0

					if IgnoreNoStock then
						if StockCount > 0 then
							NewList[Item.Name] = StockCount
						end
					else
						EggStock[Item.Name] = StockCount
					end
				end
			end
		end
	end

	return IgnoreNoStock and NewList or EggStock
end

-- Function to buy egg (simplified)
local function BuyEgg(Egg)
	-- Try different possible names for egg buying
	local possibleNames = {
		"BuyEggStock",
		"BuyEgg",
		"PurchaseEgg",
		"BuyPetEgg",
		"PetEggPurchase",
		"EggPurchase",
		"BuyPet",
		"PurchasePet"
	}

	for _, eventName in pairs(possibleNames) do
		local event = GameEvents:FindFirstChild(eventName)
		if event and event:IsA("RemoteEvent") then
			event:FireServer(Egg)
			break
		end
	end
end

-- Function to buy all selected eggs (simplified)
local function BuyAllSelectedEggs()
    local selectedEggs = SelectedEggStock.Selected
    if type(selectedEggs) ~= "table" then return end

    -- Check if we have any selected eggs - handle both table and other formats
    local eggsToProcess = {}

    for k, v in pairs(selectedEggs) do
        if v == true or (type(v) == "string" and v ~= "") then
            table.insert(eggsToProcess, type(k) == "string" and k or v)
        elseif type(k) == "number" and type(v) == "string" and v ~= "" then
            table.insert(eggsToProcess, v)
        end
    end

    if #eggsToProcess == 0 then return end

    -- Update stock before buying
    GetEggStock()

    -- Iterate through all selected eggs
    for _, eggName in pairs(eggsToProcess) do
        local stock = EggStock[eggName]

        -- Only buy if egg is in stock
        if stock and stock > 0 then
            for i = 1, stock do
                BuyEgg(eggName)
            end
        end
    end
end

-- Function to buy all selected gears (simplified)
local function BuyAllSelectedGears()
    local selectedGears = SelectedGearStock.Selected
    if type(selectedGears) ~= "table" then return end

    -- Check if we have any selected gears - handle both table and other formats
    local gearsToProcess = {}

    for k, v in pairs(selectedGears) do
        if v == true or (type(v) == "string" and v ~= "") then
            table.insert(gearsToProcess, type(k) == "string" and k or v)
        elseif type(k) == "number" and type(v) == "string" and v ~= "" then
            table.insert(gearsToProcess, v)
        end
    end

    if #gearsToProcess == 0 then return end

    -- Update stock before buying
    GetGearStock()

    -- Iterate through all selected gears
    for _, gearName in pairs(gearsToProcess) do
        local stock = GearStock[gearName]

        -- Only buy if gear is in stock
        if stock and stock > 0 then
            for i = 1, stock do
                BuyGear(gearName)
            end
        end
    end
end

-- Function to buy all selected seeds (simplified)
local function BuyAllSelectedSeeds()
    local selectedSeeds = SelectedSeedStock.Selected
    if type(selectedSeeds) ~= "table" then return end

    -- Check if we have any selected seeds - handle both table and other formats
    local seedsToProcess = {}

    for k, v in pairs(selectedSeeds) do
        if v == true or (type(v) == "string" and v ~= "") then
            table.insert(seedsToProcess, type(k) == "string" and k or v)
        elseif type(k) == "number" and type(v) == "string" and v ~= "" then
            table.insert(seedsToProcess, v)
        end
    end

    if #seedsToProcess == 0 then return end

    -- Update stock before buying
    GetSeedStock()

    -- Iterate through all selected seeds
    for _, seedName in pairs(seedsToProcess) do
        local stock = SeedStock[seedName]

        -- Only buy if seed is in stock
        if stock and stock > 0 then
            for i = 1, stock do
                BuySeed(seedName)
            end
        end
    end
end

-- Function to check stock changes and auto-buy (simplified)
local function startStockChecking()
    stockCheckConnection = RunService.Heartbeat:Connect(function()
        if not autoBuyEnabled or not SelectedSeedStock.Selected then return end

        -- Check if we have selected seeds
        local selectedSeeds = SelectedSeedStock.Selected
        if type(selectedSeeds) ~= "table" then return end

        local hasSelectedSeeds = false
        for k, v in pairs(selectedSeeds) do
            if v == true or (type(v) == "string" and v ~= "") then
                hasSelectedSeeds = true
                break
            end
        end

        if not hasSelectedSeeds then return end

        local currentStock = GetSeedStock()
        local shouldBuy = false

        -- Process selected seeds based on their format
        local seedsToCheck = {}
        for k, v in pairs(selectedSeeds) do
            if v == true or (type(v) == "string" and v ~= "") then
                local seedName = type(k) == "string" and k or v
                if type(seedName) == "string" and seedName ~= "" then
                    table.insert(seedsToCheck, seedName)
                end
            elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                table.insert(seedsToCheck, v)
            end
        end

        -- Check if stock has changed for any selected seed OR if we haven't checked before
        for _, seedName in pairs(seedsToCheck) do
            local previousStock = lastStockCheck[seedName] or -1
            local newStock = currentStock[seedName] or 0

            if previousStock ~= newStock then
                lastStockCheck[seedName] = newStock
                if newStock > 0 then
                    shouldBuy = true
                end
            end
        end

        if shouldBuy then
            BuyAllSelectedSeeds()
        end
    end)
end

-- Function to stop stock checking
local function stopStockChecking()
    if stockCheckConnection then
        stockCheckConnection:Disconnect()
        stockCheckConnection = nil
    end
end

-- Function to check gear stock changes and auto-buy (simplified)
local function startGearStockChecking()
    gearStockCheckConnection = RunService.Heartbeat:Connect(function()
        if not gearAutoBuyEnabled or not SelectedGearStock.Selected then return end

        -- Check if we have selected gears
        local selectedGears = SelectedGearStock.Selected
        if type(selectedGears) ~= "table" then return end

        local hasSelectedGears = false
        for k, v in pairs(selectedGears) do
            if v == true or (type(v) == "string" and v ~= "") then
                hasSelectedGears = true
                break
            end
        end

        if not hasSelectedGears then return end

        local currentStock = GetGearStock()
        local shouldBuy = false

        -- Process selected gears based on their format
        local gearsToCheck = {}
        for k, v in pairs(selectedGears) do
            if v == true or (type(v) == "string" and v ~= "") then
                local gearName = type(k) == "string" and k or v
                if type(gearName) == "string" and gearName ~= "" then
                    table.insert(gearsToCheck, gearName)
                end
            elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                table.insert(gearsToCheck, v)
            end
        end

        -- Check if stock has changed for any selected gear OR if we haven't checked before
        for _, gearName in pairs(gearsToCheck) do
            local previousStock = lastGearStockCheck[gearName] or -1
            local newStock = currentStock[gearName] or 0

            if previousStock ~= newStock then
                lastGearStockCheck[gearName] = newStock
                if newStock > 0 then
                    shouldBuy = true
                end
            end
        end

        if shouldBuy then
            BuyAllSelectedGears()
        end
    end)
end

-- Function to stop gear stock checking
local function stopGearStockChecking()
    if gearStockCheckConnection then
        gearStockCheckConnection:Disconnect()
        gearStockCheckConnection = nil
    end
end

-- Function to check egg stock changes and auto-buy (simplified)
local function startEggStockChecking()
    eggStockCheckConnection = RunService.Heartbeat:Connect(function()
        if not eggAutoBuyEnabled or not SelectedEggStock.Selected then return end

        -- Check if we have selected eggs
        local selectedEggs = SelectedEggStock.Selected
        if type(selectedEggs) ~= "table" then return end

        local hasSelectedEggs = false
        for k, v in pairs(selectedEggs) do
            if v == true or (type(v) == "string" and v ~= "") then
                hasSelectedEggs = true
                break
            end
        end

        if not hasSelectedEggs then return end

        local currentStock = GetEggStock()
        local shouldBuy = false

        -- Process selected eggs based on their format
        local eggsToCheck = {}
        for k, v in pairs(selectedEggs) do
            if v == true or (type(v) == "string" and v ~= "") then
                local eggName = type(k) == "string" and k or v
                if type(eggName) == "string" and eggName ~= "" then
                    table.insert(eggsToCheck, eggName)
                end
            elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                table.insert(eggsToCheck, v)
            end
        end

        -- Check if stock has changed for any selected egg OR if we haven't checked before
        for _, eggName in pairs(eggsToCheck) do
            local previousStock = lastEggStockCheck[eggName] or -1
            local newStock = currentStock[eggName] or 0

            if previousStock ~= newStock then
                lastEggStockCheck[eggName] = newStock
                if newStock > 0 then
                    shouldBuy = true
                end
            end
        end

        if shouldBuy then
            BuyAllSelectedEggs()
        end
    end)
end

-- Function to stop egg stock checking
local function stopEggStockChecking()
    if eggStockCheckConnection then
        eggStockCheckConnection:Disconnect()
        eggStockCheckConnection = nil
    end
end

local Window = Rayfield:CreateWindow({
    Name = "หมา " .. 1.0,
    LoadingTitle = "by XZery",
    LoadingSubtitle = "Loading...",
    ConfigurationSaving = {
        Enabled = true,
        FolderName = "RayfieldScriptHub",
        FileName = "grow-a-garden"
    },
    Discord = {
        Enabled = false,
        Invite = "noinvitelink",
        RememberJoins = true
    },
    KeySystem = false,
    KeySettings = {
        Title = "Untitled",
        Subtitle = "Key System",
        Note = "No method of obtaining the key is provided",
        FileName = "Key",
        SaveKey = true,
        GrabKeyFromSite = false,
        Key = {"Hello"}
    }
})

local Tabs = {
    Main = Window:CreateTab("Main", 4483362458),
    Seed = Window:CreateTab("Seed", 4483362458),
    Gear = Window:CreateTab("Gear", 4483362458),
    Egg = Window:CreateTab("Egg", 4483362458)
}

local speed = Tabs.Main:CreateSlider({
    Name = "Speed",
    Range = {16, 200},
    Increment = 1,
    Suffix = "",
    CurrentValue = 16,
    Flag = "SpeedSlider",
    Callback = function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        if char and char:FindFirstChild("Humanoid") then
            char.Humanoid.WalkSpeed = Value
        end
    end,
})

local jump = Tabs.Main:CreateSlider({
    Name = "Jump Power",
    Range = {50, 200},
    Increment = 1,
    Suffix = "",
    CurrentValue = 50,
    Flag = "JumpSlider",
    Callback = function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        if char and char:FindFirstChild("Humanoid") then
            char.Humanoid.JumpPower = Value
        end
    end,
})

    -- Fly toggle functionality with mobile support (optimized)
    local flyEnabled = false
    local bodyVelocity = nil
    local bodyAngularVelocity = nil
    local connection = nil
    local flySpeed = 50

    -- Mobile fly controls
    local flyUpButton = nil
    local flyDownButton = nil
    local mobileControlsFrame = nil
    local isTouchEnabled = UserInputService.TouchEnabled

    -- Cache color values
    local upButtonColor = Color3.fromRGB(0, 162, 255)
    local downButtonColor = Color3.fromRGB(255, 87, 87)
    local whiteColor = Color3.fromRGB(255, 255, 255)

    local function createMobileControls()
        if not isTouchEnabled then return end

        -- Create mobile fly controls
        local screenGui = Instance.new("ScreenGui")
        screenGui.Name = "FlyControls"
        screenGui.Parent = LocalPlayer.PlayerGui
        screenGui.ResetOnSpawn = false

        mobileControlsFrame = Instance.new("Frame")
        mobileControlsFrame.Name = "FlyControlsFrame"
        mobileControlsFrame.Size = UDim2.new(0, 120, 0, 200)
        mobileControlsFrame.Position = UDim2.new(1, -130, 0.5, -100)
        mobileControlsFrame.BackgroundTransparency = 1
        mobileControlsFrame.Parent = screenGui

        -- Fly Up Button
        flyUpButton = Instance.new("TextButton")
        flyUpButton.Name = "FlyUpButton"
        flyUpButton.Size = UDim2.new(0, 100, 0, 80)
        flyUpButton.Position = UDim2.new(0, 10, 0, 10)
        flyUpButton.BackgroundColor3 = upButtonColor
        flyUpButton.BorderSizePixel = 0
        flyUpButton.Text = "UP"
        flyUpButton.TextColor3 = whiteColor
        flyUpButton.TextScaled = true
        flyUpButton.Font = Enum.Font.GothamBold
        flyUpButton.Parent = mobileControlsFrame

        -- Add rounded corners to up button
        local upCorner = Instance.new("UICorner")
        upCorner.CornerRadius = UDim.new(0, 10)
        upCorner.Parent = flyUpButton

        -- Fly Down Button
        flyDownButton = Instance.new("TextButton")
        flyDownButton.Name = "FlyDownButton"
        flyDownButton.Size = UDim2.new(0, 100, 0, 80)
        flyDownButton.Position = UDim2.new(0, 10, 0, 110)
        flyDownButton.BackgroundColor3 = downButtonColor
        flyDownButton.BorderSizePixel = 0
        flyDownButton.Text = "DOWN"
        flyDownButton.TextColor3 = whiteColor
        flyDownButton.TextScaled = true
        flyDownButton.Font = Enum.Font.GothamBold
        flyDownButton.Parent = mobileControlsFrame

        -- Add rounded corners to down button
        local downCorner = Instance.new("UICorner")
        downCorner.CornerRadius = UDim.new(0, 10)
        downCorner.Parent = flyDownButton
    end

    local function removeMobileControls()
        if mobileControlsFrame and mobileControlsFrame.Parent then
            mobileControlsFrame.Parent:Destroy()
        end
        flyUpButton = nil
        flyDownButton = nil
        mobileControlsFrame = nil
    end

    local function enableFly()
        local player = game.Players.LocalPlayer
        local char = player.Character
        if not char or not char:FindFirstChild("HumanoidRootPart") then return end

        local humanoidRootPart = char.HumanoidRootPart

        -- Create BodyVelocity for movement
        bodyVelocity = Instance.new("BodyVelocity")
        bodyVelocity.MaxForce = Vector3.new(9e9, 9e9, 9e9)
        bodyVelocity.Velocity = Vector3.new(0, 0, 0)
        bodyVelocity.Parent = humanoidRootPart

        -- Create BodyAngularVelocity for rotation control
        bodyAngularVelocity = Instance.new("BodyAngularVelocity")
        bodyAngularVelocity.MaxTorque = Vector3.new(0, 9e9, 0)
        bodyAngularVelocity.AngularVelocity = Vector3.new(0, 0, 0)
        bodyAngularVelocity.Parent = humanoidRootPart

        -- Create mobile controls if on mobile
        createMobileControls()

        -- Movement control (optimized)
        connection = RunService.Heartbeat:Connect(function()
            local velocity = Vector3.new(0, 0, 0)

            -- Get camera directions
            local lookDirection = camera.CFrame.LookVector
            local rightDirection = camera.CFrame.RightVector
            local upDirection = Vector3.new(0, 1, 0)

            -- PC Controls (Keyboard)
            if not isTouchEnabled then
                if UserInputService:IsKeyDown(Enum.KeyCode.W) then
                    velocity = velocity + lookDirection * flySpeed
                end
                if UserInputService:IsKeyDown(Enum.KeyCode.S) then
                    velocity = velocity - lookDirection * flySpeed
                end
                if UserInputService:IsKeyDown(Enum.KeyCode.A) then
                    velocity = velocity - rightDirection * flySpeed
                end
                if UserInputService:IsKeyDown(Enum.KeyCode.D) then
                    velocity = velocity + rightDirection * flySpeed
                end
                if UserInputService:IsKeyDown(Enum.KeyCode.Space) then
                    velocity = velocity + upDirection * flySpeed
                end
                if UserInputService:IsKeyDown(Enum.KeyCode.LeftShift) then
                    velocity = velocity - upDirection * flySpeed
                end
            else
                -- Mobile Controls
                local moveVector = char.Humanoid.MoveDirection
                if moveVector.Magnitude > 0 then
                    -- Use the movement direction from mobile control
                    local cameraCFrame = camera.CFrame
                    local relativeDirection = cameraCFrame:VectorToWorldSpace(Vector3.new(moveVector.X, 0, -moveVector.Z))
                    velocity = velocity + relativeDirection * flySpeed
                end

                -- Handle up/down movement with buttons
                if flyUpButton and flyUpButton.Parent then
                    -- Connect touch events for up button
                    if not flyUpButton:GetAttribute("Connected") then
                        flyUpButton:SetAttribute("Connected", true)
                        flyUpButton.MouseButton1Down:Connect(function()
                            flyUpButton:SetAttribute("Pressed", true)
                        end)
                        flyUpButton.MouseButton1Up:Connect(function()
                            flyUpButton:SetAttribute("Pressed", false)
                        end)
                    end

                    -- Connect touch events for down button
                    if flyDownButton and not flyDownButton:GetAttribute("Connected") then
                        flyDownButton:SetAttribute("Connected", true)
                        flyDownButton.MouseButton1Down:Connect(function()
                            flyDownButton:SetAttribute("Pressed", true)
                        end)
                        flyDownButton.MouseButton1Up:Connect(function()
                            flyDownButton:SetAttribute("Pressed", false)
                        end)
                    end

                    -- Check button states
                    if flyUpButton:GetAttribute("Pressed") then
                        velocity = velocity + upDirection * flySpeed
                    end
                    if flyDownButton and flyDownButton:GetAttribute("Pressed") then
                        velocity = velocity - upDirection * flySpeed
                    end
                end
            end

            bodyVelocity.Velocity = velocity
        end)
    end

    local function disableFly()
        if bodyVelocity then
            bodyVelocity:Destroy()
            bodyVelocity = nil
        end
        if bodyAngularVelocity then
            bodyAngularVelocity:Destroy()
            bodyAngularVelocity = nil
        end
        if connection then
            connection:Disconnect()
            connection = nil
        end
        -- Remove mobile controls
        removeMobileControls()
    end

    local flyToggle = Tabs.Main:CreateToggle({
        Name = "Fly",
        CurrentValue = false,
        Flag = "FlyToggle",
        Callback = function(Value)
            flyEnabled = Value
            if flyEnabled then
                enableFly()
            else
                disableFly()
            end
        end,
    })

    -- Anti-AFK Toggle
    local antiAfkToggle = Tabs.Main:CreateToggle({
        Name = "Anti-AFK",
        CurrentValue = false,
        Flag = "AntiAfkToggle",
        Callback = function(Value)
            antiAfkEnabled = Value
            if antiAfkEnabled then
                startAntiAfk()
            else
                stopAntiAfk()
            end
        end,
    })

    -- Reset Character Button
    local resetButton = Tabs.Main:CreateButton({
        Name = "Reset Character",
        Callback = function()
            local player = game.Players.LocalPlayer
            if player.Character then
                player.Character:BreakJoints()
            end
        end,
    })

    -- Seed stock dropdown (multi-select) with auto-save
    local seedDropdown = Tabs.Seed:CreateDropdown({
        Name = "Select Seeds",
        Options = {},
        CurrentOption = {},
        MultipleOptions = true,
        Flag = "SeedDropdown", -- This flag enables auto-save in Rayfield
        Callback = function(Value)
            SelectedSeedStock.Selected = Value
            GetSeedStock()
        end,
    })

    -- Function to update seed dropdown
    local function updateSeedDropdown()
        local allSeeds = GetSeedStock(false) -- Get all seeds regardless of stock
        local seedList = {}

        for seedName, _ in pairs(allSeeds) do
            table.insert(seedList, seedName)
        end

        seedDropdown:Refresh(seedList, true)
    end

    -- Auto-buy toggle - moved to Seed tab
    local autoBuyToggle = Tabs.Seed:CreateToggle({
        Name = "Auto-Buy Seeds",
        CurrentValue = false,
        Flag = "AutoBuyToggle",
        Callback = function(Value)
            autoBuyEnabled = Value
            if autoBuyEnabled then
                startStockChecking()
            else
                stopStockChecking()
            end
        end,
    })

    -- Note: Callback is now handled in the dropdown creation above



    -- Initial seed dropdown update
    updateSeedDropdown()

    -- Load saved seed selections (will be loaded after SaveManager is properly initialized)

    -- Gear stock dropdown (multi-select) with auto-save
    local gearDropdown = Tabs.Gear:CreateDropdown({
        Name = "Select Gears",
        Options = {},
        CurrentOption = {},
        MultipleOptions = true,
        Flag = "GearDropdown", -- This flag enables auto-save in Rayfield
        Callback = function(Value)
            SelectedGearStock.Selected = Value
            GetGearStock()
        end,
    })

    -- Function to update gear dropdown
    local function updateGearDropdown()
        local allGears = GetGearStock(false) -- Get all gears regardless of stock
        local gearList = {}

        for gearName, _ in pairs(allGears) do
            table.insert(gearList, gearName)
        end

        gearDropdown:Refresh(gearList, true)
    end

    -- Gear auto-buy toggle
    local gearAutoBuyToggle = Tabs.Gear:CreateToggle({
        Name = "Auto-Buy Gears",
        CurrentValue = false,
        Flag = "GearAutoBuyToggle",
        Callback = function(Value)
            gearAutoBuyEnabled = Value
            if gearAutoBuyEnabled then
                startGearStockChecking()
            else
                stopGearStockChecking()
            end
        end,
    })

    -- Note: Callback is now handled in the dropdown creation above

    -- Initial gear dropdown update
    updateGearDropdown()

    -- Egg stock dropdown (multi-select) with auto-save
    local eggDropdown = Tabs.Egg:CreateDropdown({
        Name = "Select Eggs",
        Options = {},
        CurrentOption = {},
        MultipleOptions = true,
        Flag = "EggDropdown", -- This flag enables auto-save in Rayfield
        Callback = function(Value)
            SelectedEggStock.Selected = Value
            GetEggStock()
        end,
    })

    -- Function to update egg dropdown
    local function updateEggDropdown()
        local allEggs = GetEggStock(false) -- Get all eggs regardless of stock
        local eggList = {}

        for eggName, _ in pairs(allEggs) do
            table.insert(eggList, eggName)
        end

        eggDropdown:Refresh(eggList, true)
    end

    -- Egg auto-buy toggle
    local eggAutoBuyToggle = Tabs.Egg:CreateToggle({
        Name = "Auto-Buy Eggs",
        CurrentValue = false,
        Flag = "EggAutoBuyToggle",
        Callback = function(Value)
            eggAutoBuyEnabled = Value
            if eggAutoBuyEnabled then
                startEggStockChecking()
            else
                stopEggStockChecking()
            end
        end,
    })

    -- Initial egg dropdown update
    updateEggDropdown()

    -- Function to manually load saved selections
    local function loadSavedSelections()
        -- Wait a moment for Rayfield to initialize
        task.wait(1)

        -- Get saved selections from the config file
        local success, result = pcall(function()
            local file = readfile("RayfieldScriptHub/grow-a-garden.json")
            return game:GetService("HttpService"):JSONDecode(file)
        end)

        if success and result then
            -- Apply saved seed selections
            if result.SeedDropdown and #result.SeedDropdown > 0 then
                SelectedSeedStock.Selected = result.SeedDropdown
                seedDropdown:Set(result.SeedDropdown)
            end

            -- Apply saved gear selections
            if result.GearDropdown and #result.GearDropdown > 0 then
                SelectedGearStock.Selected = result.GearDropdown
                gearDropdown:Set(result.GearDropdown)
            end

            -- Apply saved egg selections
            if result.EggDropdown and #result.EggDropdown > 0 then
                SelectedEggStock.Selected = result.EggDropdown
                eggDropdown:Set(result.EggDropdown)
            end

            -- Apply saved toggle states
            if result.AutoBuyToggle ~= nil then
                autoBuyToggle:Set(result.AutoBuyToggle)
            end

            if result.GearAutoBuyToggle ~= nil then
                gearAutoBuyToggle:Set(result.GearAutoBuyToggle)
            end

            if result.EggAutoBuyToggle ~= nil then
                eggAutoBuyToggle:Set(result.EggAutoBuyToggle)
            end
        end
    end

    -- Load saved selections after a delay
    spawn(loadSavedSelections)

-- Rayfield has built-in configuration saving, so we don't need separate SaveManager/InterfaceManager

Rayfield:Notify({
    Title = "Script Loaded",
    Content = "The script has been loaded successfully.",
    Duration = 6.5,
    Image = 4483362458,
    Actions = {
        Ignore = {
            Name = "Okay!",
            Callback = function()
                -- Notification acknowledged
            end
        },
    },
})