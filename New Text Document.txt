local b='ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';local function d(e)e=string.gsub(e,'[^'..b..'=]','')return(e:gsub('.',function(x)if(x=='=')then return''end;local r,f='',(b:find(x)-1)for i=6,1,-1 do r=r..(f%2^i-f%2^(i-1)>0 and'1'or'0')end;return r;end):gsub('%d%d%d?%d?%d?%d?%d?%d?',function(x)if(#x~=8)then return''end;local c=0 for i=1,8 do c=c+(x:sub(i,i)=='1'and 2^(8-i)or 0)end;return string.char(c)end))end
local s={["1"]="aHR0cHM6Ly9zaXJpdXMubWVudS9yYXlmaWVsZA==",["2"]="UGxheWVycw==",["3"]="UmVwbGljYXRlZFN0b3JhZ2U=",["4"]="UGxheWVyR3Vp",["5"]="R2FtZUV2ZW50cw==",["6"]="U2VlZF9TaG9w",["7"]="Qmx1ZWJlcnJ5",["8"]="TWFpbl9GcmFtZQ==",["9"]="U3RvY2tfVGV4dA==",["10"]="QnV5U2VlZFN0b2Nr",["11"]="R2Vhcl9TaG9w",["12"]="V2F0ZXJpbmdDYW4=",["13"]="QnV5R2VhclN0b2Nr",["14"]="UGV0U2hvcF9VSQ==",["15"]="UGV0X0VnZ19TaG9w",["16"]="RWdnX1Nob3A=",["17"]="UGV0RWdnU2hvcA==",["18"]="RWdnU2hvcA==",["19"]="QnV5RWdnU3RvY2s=",["20"]="QnV5RWdn",["21"]="UHVyY2hhc2VFZ2c=",["22"]="QnV5UGV0RWdn",["23"]="UGV0RWdnUHVyY2hhc2U=",["24"]="RWdnUHVyY2hhc2U=",["25"]="QnV5UGV0",["26"]="UHVyY2hhc2VQZXQ=",["27"]="TWFpbg==",["28"]="U2VlZA==",["29"]="R2Vhcg==",["30"]="RWdn",["31"]="U3BlZWQ=",["32"]="SnVtcCBQb3dlcg==",["33"]="Rmx5",["34"]="UmVzZXQgQ2hhcmFjdGVy",["35"]="U2VsZWN0IFNlZWRz",["36"]="QXV0by1CdXkgU2VlZHM=",["37"]="U2VsZWN0IEdlYXJz",["38"]="QXV0by1CdXkgR2VhcnM=",["39"]="U2VsZWN0IEVnZ3M=",["40"]="QXV0by1CdXkgRWdncy",["41"]="SGVsbG8=",["42"]="U2NyaXB0IExvYWRlZA==",["43"]="VGhlIHNjcmlwdCBoYXMgYmVlbiBsb2FkZWQgc3VjY2Vzc2Z1bGx5Lg==",["44"]="T2theSE="};local function g(i)return d(s[tostring(i)])end
local a1=loadstring(game:HttpGet(g(1)))()local a2=game:GetService(g(2))local a3=game:GetService(g(3))local a4=a2.LocalPlayer local a5=a4.PlayerGui local a6=a3.GameEvents local a7={}local a8={Selected={}}local a9={}local a10=false local a11=nil local a12={}local a13={Selected={}}local a14={}local a15=false local a16=nil local a17={}local a18={Selected={}}local a19={}local a20=false local a21=nil
local function f1(b1)local b2=a5[g(6)]local b3=b2:FindFirstChild(g(7),true).Parent local b4={}for _,b5 in next,b3:GetChildren()do local b6=b5:FindFirstChild(g(8))if not b6 then continue end;local b7=b6[g(9)].Text local b8=tonumber(b7:match("%d+"))if b1 then if b8<=0 then continue end;b4[b5.Name]=b8 continue end;a7[b5.Name]=b8 end;return b1 and b4 or a7 end
local function f2(b9)a6[g(10)]:FireServer(b9)end
local function f3(b1)local b2=a5[g(11)]if not b2 then print("Gear_Shop not found")return{}end;local b3=b2:FindFirstChild(g(12))if not b3 then for _,c in pairs(b2:GetDescendants())do if c:IsA("Frame")and c:FindFirstChild(g(8))then b3=c.Parent break end end end;if not b3 then print("Could not find gear items container")return{}end;local b4={}for _,b5 in next,b3:GetChildren()do local b6=b5:FindFirstChild(g(8))if not b6 then continue end;local b7=b6:FindFirstChild(g(9))if not b7 then continue end;local b8=tonumber(b7.Text:match("%d+"))or 0;if b1 then if b8<=0 then continue end;b4[b5.Name]=b8 continue end;a12[b5.Name]=b8 end;return b1 and b4 or a12 end
local function f4(b9)a6[g(13)]:FireServer(b9)end
local function f5(b1)local b2=a5:FindFirstChild(g(14))or a5:FindFirstChild(g(15))or a5:FindFirstChild(g(16))or a5:FindFirstChild(g(17))or a5:FindFirstChild(g(18))if not b2 then print("Egg_Shop not found")return{}end;local b3=nil;for _,c in pairs(b2:GetDescendants())do if c:IsA("Frame")and c:FindFirstChild(g(8))then b3=c.Parent break end end;if not b3 then for _,c in pairs(b2:GetDescendants())do if c:IsA("ScrollingFrame")or c:IsA("Frame")then local d=0 for _,e in pairs(c:GetChildren())do if e:IsA("Frame")then d=d+1 end end;if d>1 then b3=c break end end end end;if not b3 then print("Could not find egg items container")return{}end;local b4={}for _,b5 in next,b3:GetChildren()do if not b5:IsA("Frame")then continue end;local b6=b5:FindFirstChild(g(8))if not b6 then continue end;local b7=b6:FindFirstChild(g(9))if not b7 then continue end;local b8=tonumber(b7.Text:match("%d+"))or 0;if b1 then if b8<=0 then continue end;b4[b5.Name]=b8 continue end;a17[b5.Name]=b8 end;return b1 and b4 or a17 end
local function f6(b9)local c={"BuyEggStock","BuyEgg","PurchaseEgg","BuyPetEgg","PetEggPurchase","EggPurchase","BuyPet","PurchasePet"}local d=nil for _,e in pairs(c)do local f=a6:FindFirstChild(e)if f and f:IsA("RemoteEvent")then d=f break end end;if d then d:FireServer(b9)else print("ERROR: No egg buying remote event found!")end end
local function f7()local b=a18.Selected if type(b)=="table"then local c={}for d,e in pairs(b)do if e==true or(type(e)=="string"and e~="")then table.insert(c,type(d)=="string"and d or e)elseif type(d)=="number"and type(e)=="string"and e~=""then table.insert(c,e)end end;if# c==0 then return end;f5()for _,d in pairs(c)do local e=a17[d]if e and e>0 then for i=1,e do f6(d)end end end end end
local function f8()local b=a13.Selected if type(b)=="table"then local c={}for d,e in pairs(b)do if e==true or(type(e)=="string"and e~="")then table.insert(c,type(d)=="string"and d or e)elseif type(d)=="number"and type(e)=="string"and e~=""then table.insert(c,e)end end;if# c==0 then return end;f3()for _,d in pairs(c)do local e=a12[d]if e and e>0 then for i=1,e do f4(d)end end end end end
local function f9()local b=a8.Selected if type(b)=="table"then local c={}for d,e in pairs(b)do if e==true or(type(e)=="string"and e~="")then table.insert(c,type(d)=="string"and d or e)elseif type(d)=="number"and type(e)=="string"and e~=""then table.insert(c,e)end end;if# c==0 then return end;f1()for _,d in pairs(c)do local e=a7[d]if e and e>0 then for i=1,e do f2(d)end end end end end
local function f10()a11=game:GetService("RunService").Heartbeat:Connect(function()if not a10 or not a8.Selected then return end;local b=false if type(a8.Selected)=="table"then for c,d in pairs(a8.Selected)do if d==true or(type(d)=="string"and d~="")then b=true break end end end;if not b then return end;local c=f1()local d=a8.Selected local e=false local f={}if type(d)=="table"then for g,h in pairs(d)do if h==true or(type(h)=="string"and h~="")then local i=type(g)=="string"and g or h if type(i)=="string"and i~=""then table.insert(f,i)end elseif type(g)=="number"and type(h)=="string"and h~=""then table.insert(f,h)end end end;for _,g in pairs(f)do local h=a9[g]or -1 if h~=c[g]then a9[g]=c[g]if c[g]and c[g]>0 then e=true end end end;if e then f9()end end)end
local function f11()if a11 then a11:Disconnect()a11=nil end end
local function f12()a16=game:GetService("RunService").Heartbeat:Connect(function()if not a15 or not a13.Selected then return end;local b=false if type(a13.Selected)=="table"then for c,d in pairs(a13.Selected)do if d==true or(type(d)=="string"and d~="")then b=true break end end end;if not b then return end;local c=f3()local d=a13.Selected local e=false local f={}if type(d)=="table"then for g,h in pairs(d)do if h==true or(type(h)=="string"and h~="")then local i=type(g)=="string"and g or h if type(i)=="string"and i~=""then table.insert(f,i)end elseif type(g)=="number"and type(h)=="string"and h~=""then table.insert(f,h)end end end;for _,g in pairs(f)do local h=a14[g]or -1 if h~=c[g]then a14[g]=c[g]if c[g]and c[g]>0 then e=true end end end;if e then f8()end end)end
local function f13()if a16 then a16:Disconnect()a16=nil end end
local function f14()a21=game:GetService("RunService").Heartbeat:Connect(function()if not a20 or not a18.Selected then return end;local b=false if type(a18.Selected)=="table"then for c,d in pairs(a18.Selected)do if d==true or(type(d)=="string"and d~="")then b=true break end end end;if not b then return end;local c=f5()local d=a18.Selected local e=false local f={}if type(d)=="table"then for g,h in pairs(d)do if h==true or(type(h)=="string"and h~="")then local i=type(g)=="string"and g or h if type(i)=="string"and i~=""then table.insert(f,i)end elseif type(g)=="number"and type(h)=="string"and h~=""then table.insert(f,h)end end end;for _,g in pairs(f)do local h=a19[g]or -1 if h~=c[g]then a19[g]=c[g]if c[g]and c[g]>0 then e=true end end end;if e then f7()end end)end
local function f15()if a21 then a21:Disconnect()a21=nil end end
local w=a1:CreateWindow({Name="หมา "..(2-1),LoadingTitle="by XZery",LoadingSubtitle="Loading...",ConfigurationSaving={Enabled=true,FolderName="RayfieldScriptHub",FileName="grow-a-garden"},Discord={Enabled=false,Invite="noinvitelink",RememberJoins=true},KeySystem=false,KeySettings={Title="Untitled",Subtitle="Key System",Note="No method of obtaining the key is provided",FileName="Key",SaveKey=true,GrabKeyFromSite=false,Key={g(41)}}})local t={m1=w:CreateTab(g(27),4483362458),m2=w:CreateTab(g(28),4483362458),m3=w:CreateTab(g(29),4483362458),m4=w:CreateTab(g(30),4483362458)}local s1=t.m1:CreateSlider({Name=g(31),Range={4*4,100*2},Increment=2-1,Suffix="",CurrentValue=8*2,Flag="SpeedSlider",Callback=function(v)local p=a2.LocalPlayer local c=p.Character if c and c:FindFirstChild("Humanoid")then c.Humanoid.WalkSpeed=v end end})local j1=t.m1:CreateSlider({Name=g(32),Range={25*2,100*2},Increment=2-1,Suffix="",CurrentValue=25*2,Flag="JumpSlider",Callback=function(v)local p=a2.LocalPlayer local c=p.Character if c and c:FindFirstChild("Humanoid")then c.Humanoid.JumpPower=v end end})local f16=false local f17=nil local f18=nil local f19=nil local f20=25*2 local f21=nil local f22=nil local f23=nil local function f24()if not game:GetService("UserInputService").TouchEnabled then return end;local s=Instance.new("ScreenGui")s.Name="FlyControls"s.Parent=a2.LocalPlayer.PlayerGui s.ResetOnSpawn=false f23=Instance.new("Frame")f23.Name="FlyControlsFrame"f23.Size=UDim2.new(0,120,0,200)f23.Position=UDim2.new(1,-130,0.5,-100)f23.BackgroundTransparency=1 f23.Parent=s f21=Instance.new("TextButton")f21.Name="FlyUpButton"f21.Size=UDim2.new(0,100,0,80)f21.Position=UDim2.new(0,10,0,10)f21.BackgroundColor3=Color3.fromRGB(0,162,255)f21.BorderSizePixel=0 f21.Text="UP"f21.TextColor3=Color3.fromRGB(255,255,255)f21.TextScaled=true f21.Font=Enum.Font.GothamBold f21.Parent=f23 local u=Instance.new("UICorner")u.CornerRadius=UDim.new(0,10)u.Parent=f21 f22=Instance.new("TextButton")f22.Name="FlyDownButton"f22.Size=UDim2.new(0,100,0,80)f22.Position=UDim2.new(0,10,0,110)f22.BackgroundColor3=Color3.fromRGB(255,87,87)f22.BorderSizePixel=0 f22.Text="DOWN"f22.TextColor3=Color3.fromRGB(255,255,255)f22.TextScaled=true f22.Font=Enum.Font.GothamBold f22.Parent=f23 local d=Instance.new("UICorner")d.CornerRadius=UDim.new(0,10)d.Parent=f22 end local function f25()if f23 and f23.Parent then f23.Parent:Destroy()end f21=nil f22=nil f23=nil end local function f26()local p=a2.LocalPlayer local c=p.Character if not c or not c:FindFirstChild("HumanoidRootPart")then return end;local h=c.HumanoidRootPart f17=Instance.new("BodyVelocity")f17.MaxForce=Vector3.new(9e9,9e9,9e9)f17.Velocity=Vector3.new(0,0,0)f17.Parent=h f18=Instance.new("BodyAngularVelocity")f18.MaxTorque=Vector3.new(0,9e9,0)f18.AngularVelocity=Vector3.new(0,0,0)f18.Parent=h f24()f19=game:GetService("RunService").Heartbeat:Connect(function()local cam=workspace.CurrentCamera local uis=game:GetService("UserInputService")local v=Vector3.new(0,0,0)local l=cam.CFrame.LookVector local r=cam.CFrame.RightVector local u=Vector3.new(0,1,0)if not uis.TouchEnabled then if uis:IsKeyDown(Enum.KeyCode.W)then v=v+l*f20 end if uis:IsKeyDown(Enum.KeyCode.S)then v=v-l*f20 end if uis:IsKeyDown(Enum.KeyCode.A)then v=v-r*f20 end if uis:IsKeyDown(Enum.KeyCode.D)then v=v+r*f20 end if uis:IsKeyDown(Enum.KeyCode.Space)then v=v+u*f20 end if uis:IsKeyDown(Enum.KeyCode.LeftShift)then v=v-u*f20 end else local m=c.Humanoid.MoveDirection if m.Magnitude>0 then local rc=cam.CFrame:VectorToWorldSpace(Vector3.new(m.X,0,-m.Z))v=v+rc*f20 end if f21 and f21.Parent then if not f21:GetAttribute("Connected")then f21:SetAttribute("Connected",true)f21.TouchTap:Connect(function()end)f21.MouseButton1Down:Connect(function()f21:SetAttribute("Pressed",true)end)f21.MouseButton1Up:Connect(function()f21:SetAttribute("Pressed",false)end)end if f22 and not f22:GetAttribute("Connected")then f22:SetAttribute("Connected",true)f22.TouchTap:Connect(function()end)f22.MouseButton1Down:Connect(function()f22:SetAttribute("Pressed",true)end)f22.MouseButton1Up:Connect(function()f22:SetAttribute("Pressed",false)end)end if f21:GetAttribute("Pressed")then v=v+u*f20 end if f22 and f22:GetAttribute("Pressed")then v=v-u*f20 end end end f17.Velocity=v end)end local function f27()if f17 then f17:Destroy()f17=nil end if f18 then f18:Destroy()f18=nil end if f19 then f19:Disconnect()f19=nil end f25()end local f28=t.m1:CreateToggle({Name=g(33),CurrentValue=false,Flag="FlyToggle",Callback=function(v)f16=v if f16 then f26()else f27()end end})local r1=t.m1:CreateButton({Name=g(34),Callback=function()local p=a2.LocalPlayer if p.Character then p.Character:BreakJoints()end end})local d1=t.m2:CreateDropdown({Name=g(35),Options={},CurrentOption={},MultipleOptions=true,Flag="SeedDropdown",Callback=function(v)a8.Selected=v f1()end})local function u1()local a=f1(false)local b={}for c,_ in pairs(a)do table.insert(b,c)end d1:Refresh(b,true)end local t1=t.m2:CreateToggle({Name=g(36),CurrentValue=false,Flag="AutoBuyToggle",Callback=function(v)a10=v if a10 then f10()else f11()end end})u1()local d2=t.m3:CreateDropdown({Name=g(37),Options={},CurrentOption={},MultipleOptions=true,Flag="GearDropdown",Callback=function(v)a13.Selected=v f3()end})local function u2()local a=f3(false)local b={}for c,_ in pairs(a)do table.insert(b,c)end d2:Refresh(b,true)end local t2=t.m3:CreateToggle({Name=g(38),CurrentValue=false,Flag="GearAutoBuyToggle",Callback=function(v)a15=v if a15 then f12()else f13()end end})u2()local d3=t.m4:CreateDropdown({Name=g(39),Options={},CurrentOption={},MultipleOptions=true,Flag="EggDropdown",Callback=function(v)a18.Selected=v f5()end})local function u3()local a=f5(false)local b={}for c,_ in pairs(a)do table.insert(b,c)end d3:Refresh(b,true)end local t3=t.m4:CreateToggle({Name=g(40),CurrentValue=false,Flag="EggAutoBuyToggle",Callback=function(v)a20=v if a20 then f14()else f15()end end})u3()a1:Notify({Title=g(42),Content=g(43),Duration=13/2,Image=4483362458,Actions={Ignore={Name=g(44),Callback=function()print("The user tapped Okay!")end}}})